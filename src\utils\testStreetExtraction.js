// 测试行政街道信息提取功能
export const testStreetExtraction = () => {
  // 模拟高德API返回的地址组件数据
  const mockAddressComponents = [
    {
      name: '北辰时代广场A1座',
      addressComponent: {
        province: '湖南省',
        city: '长沙市',
        district: '开福区',
        township: '清水塘街道', // 这是我们要的行政街道
        neighborhood: '北辰社区',
        street: '湘江中路',
        streetNumber: '188号'
      }
    },
    {
      name: '德思勤城市广场',
      addressComponent: {
        province: '湖南省',
        city: '长沙市',
        district: '雨花区',
        township: '雨花亭街道',
        neighborhood: '德思勤社区',
        street: '劳动东路',
        streetNumber: '238号'
      }
    },
    {
      name: '华远国际中心',
      addressComponent: {
        province: '湖南省',
        city: '长沙市',
        district: '天心区',
        township: '坡子街街道',
        neighborhood: '华远社区',
        street: '解放西路',
        streetNumber: '188号'
      }
    },
    {
      name: '长沙绿地中心',
      addressComponent: {
        province: '湖南省',
        city: '长沙市',
        district: '开福区',
        township: '湘江街道',
        neighborhood: '绿地社区',
        street: '湘江中路',
        streetNumber: '36号'
      }
    }
  ];

  // 提取行政街道信息的函数
  const extractAdministrativeStreet = (addressComponent) => {
    if (!addressComponent) return '';

    console.log('正在处理地址组件:', addressComponent);

    // 优先获取township（乡镇街道）
    if (addressComponent.township && addressComponent.township.includes('街道')) {
      console.log('✓ 找到行政街道 (township):', addressComponent.township);
      return addressComponent.township;
    }

    // 如果township不包含"街道"，但有township信息
    if (addressComponent.township) {
      console.log('✓ 使用township作为街道:', addressComponent.township);
      return addressComponent.township;
    }

    // 检查neighborhood（社区/居委会）是否包含街道信息
    if (addressComponent.neighborhood && addressComponent.neighborhood.includes('街道')) {
      console.log('✓ 找到行政街道 (neighborhood):', addressComponent.neighborhood);
      return addressComponent.neighborhood;
    }

    console.log('✗ 未找到行政街道信息');
    return '';
  };

  console.log('=== 开始测试行政街道信息提取 ===');

  const results = mockAddressComponents.map(item => {
    const street = extractAdministrativeStreet(item.addressComponent);
    const result = {
      building_name: item.name,
      district: item.addressComponent.district,
      township: item.addressComponent.township,
      neighborhood: item.addressComponent.neighborhood,
      extracted_street: street,
      success: !!street,
      expected: item.addressComponent.township // 期望结果
    };
    console.log('测试结果:', result);
    return result;
  });

  console.log('=== 行政街道提取测试完成 ===');
  console.log('总结:', {
    total: results.length,
    success: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    success_rate: `${Math.round((results.filter(r => r.success).length / results.length) * 100)}%`,
    correct_extractions: results.filter(r => r.extracted_street === r.expected).length
  });

  console.log('详细结果:');
  results.forEach(result => {
    const status = result.extracted_street === result.expected ? '✓' : '✗';
    console.log(`${status} ${result.building_name}: ${result.extracted_street} (期望: ${result.expected})`);
  });

  return results;
};

// 在浏览器控制台中可以直接调用
window.testStreetExtraction = testStreetExtraction;
