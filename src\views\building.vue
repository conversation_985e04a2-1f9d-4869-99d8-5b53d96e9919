<template>
  <div id="buildingBox">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <div class="building-header">
        <div class="building-info-left">
          <div class="building-title-row">
            <div class="building-title">{{ buildingInfo.name || '楼宇详情' }}</div>
            <div class="building-badges">
              <div class="star-rating" :class="`star-${buildingInfo.star || 5}`">
                <span class="star-text">{{ buildingInfo.starLevel || buildingInfo.star + '星级' }}</span>
              </div>
              <div class="green-building" v-if="buildingInfo.isGreenBuilding">
                <span class="green-text">绿色建筑</span>
              </div>
              <!-- <div class="green-building" v-else style="background: rgba(128, 128, 128, 0.15); border-color: rgba(128, 128, 128, 0.4);">
                <span class="green-text" style="color: #888; text-shadow: none;">未认证</span>
              </div> -->
              <div class="billion-building">
                <span class="billion-text">亿元楼宇</span>
              </div>
            </div>
          </div>
          <div class="building-location">
            <span class="location-icon">📍</span>
            <span>所属区域：{{ buildingInfo.district || '未知区域' }}</span>
            <span>所属街道：{{ buildingInfo.street || '未知街道' }}</span>
            <span>物业公司：{{ buildingInfo.propertyCompany || '未知物业公司' }}</span>
            <span>楼宇地址：{{ buildingInfo.address || '地址未知' }}</span>
          </div>
        </div>

        <!-- 圆形地图定位 -->
        <!-- <div class="circular-map">
          <dv-decoration-9 class="map-decoration" style="width: 210px; height: 210px;">
            <div class="map-container" ref="circularMapContainer">
              <div class="location-marker main-marker">
                <div class="marker-dot"></div>
                <div class="marker-pulse"></div>
              </div>
              <div class="direction-indicator">
                <div class="arrow-up">▲</div>
              </div>
            </div>
          </dv-decoration-9>
        </div>-->
      </div>
      <div class="building-intro">
        <div class="intro-text">
          <span class="intro-label">楼宇简介：</span>
          <span>{{ buildingInfo.description || `${buildingInfo.name}位于${buildingInfo.district}${buildingInfo.street}，交通便利，四通八达，周边拥有完善的商业配套设施，包括购物中心、酒店、餐厅等，为企业提供了便利的办公环境。` }}</span>
          <!-- <span class="expand-btn">展开</span> -->
        </div>
      </div>
    </div>

    <!-- 变更提醒区域 -->
    <div class="alert-section">
      <div class="alert-item">
        <span class="alert-icon">⚠️</span>
        <span class="alert-text">变更提醒</span>
        <span class="alert-detail">
          工商变更企业
          <span class="highlight">{{ alertData.businessChange }}</span> 家，经营风险企业
          <span class="highlight">{{ alertData.businessRisk }}</span> 家，司法风险企业
          <span class="highlight">{{ alertData.legalRisk }}</span> 家，欠租超30天企业
          <span class="highlight">{{ alertData.overdueRent }}</span> 家，即将迁出企业
          <span class="highlight">{{ alertData.migrationAlert }}</span> 家
        </span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="unified-main-content building-layout">
      <!-- 左侧面板 -->
      <div class="unified-panel unified-left-panel">
        <!-- 建筑基础信息 -->
        <div class="building-basic-info">
          <div class="basic-info-grid">
            <div class="stats-row">
              <div class="basic-info-item">
                <div class="info-label">总建筑面积</div>
                <div class="info-value">
                  {{ formatNumber(buildingInfo.buildingArea) }}
                  <span class="unit">㎡</span>
                </div>
              </div>
              <div class="basic-info-item">
                <div class="info-label">建筑高度</div>
                <div class="info-value">
                  {{ buildingInfo.buildingHeight || '0' }}
                  <span class="unit">米</span>
                </div>
              </div>
            </div>
            <div class="stats-row">
              <div class="basic-info-item">
                <div class="info-label">总层数</div>
                <div class="info-value">
                  {{ buildingInfo.totalFloors || '0' }}
                  <span class="unit">层</span>
                </div>
              </div>
              <div class="basic-info-item">
                <div class="info-label">标准层高</div>
                <div class="info-value">
                  {{ buildingInfo.standardFloorHeight || '0' }}
                  <span class="unit">米</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 入驻企业总数 -->
        <div class="enterprise-total">
          <div class="enterprise-icon">🏢</div>
          <div class="enterprise-info">
            <div class="enterprise-title">入驻企业总数</div>
            <div class="enterprise-count">
              {{ buildingInfo.totalCompanies || '10,000' }}
              <span class="unit">家</span>
            </div>
          </div>
        </div>

        <!-- 统计指标 -->
        <div class="stats-section">
          <div class="stats-row">
            <div class="stat-item">
              <div class="stat-label">注册资本</div>
              <div class="stat-value">
                {{ formatNumber(buildingInfo.registeredCapital) }}
                <span class="unit">万</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">参保人数</div>
              <div class="stat-value">
                {{ formatNumber(buildingInfo.insuredPersonnel) }}
                <span class="unit">人</span>
              </div>
            </div>
          </div>
          <div class="stats-row">
            <div class="stat-item">
              <div class="stat-label">发布招聘岗位</div>
              <div class="stat-value highlight">
                {{ formatNumber(buildingInfo.jobPositions) }}
                <span class="unit">个</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">知识产权</div>
              <div class="stat-value highlight">
                {{ formatNumber(buildingInfo.intellectualProperty) }}
                <span class="unit">项</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据分析图表 -->
        <div class="analysis-charts-left">
          <!-- 企业注册资本分析 -->
          <div class="chart-item-left">
            <div class="chart-title-wrapper">
              <div class="chart-title">企业注册资本分析</div>
            </div>
            <div ref="capitalChartRef" class="chart-container-left"></div>
          </div>
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="unified-panel unified-center-panel">
        <!-- 楼宇地图和配套设施区域 -->
        <div class="building-map-facilities-area">
          <!-- 左侧地图区域 -->
          <div class="map-section">
            <div class="map-container">
              <div class="map-title">楼宇位置及周边配套</div>
              <div ref="buildingMapRef" class="building-map">
                <!-- 地图加载状态 -->
                <div v-if="mapLoading" class="loading-overlay">
                  <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="loading-text">正在加载地图...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧配套设施列表 -->
          <div class="facilities-section">
            <div class="facilities-container">
              <div class="facilities-title">重点配套设施</div>

              <!-- 配套设施加载状态 -->
              <div v-if="facilitiesLoading" class="facilities-loading">
                <div class="loading-spinner">
                  <div class="spinner-ring"></div>
                  <div class="loading-text">正在搜索周边配套...</div>
                </div>
              </div>

              <!-- 配套设施内容 -->
              <div v-else>
                <!-- 无数据状态 -->
                <div v-if="!nearbyFacilities.transport.length && !restaurantStats.within500m && !nearbyFacilities.banks.length && !nearbyFacilities.medical.length" class="no-data">
                  <div class="no-data-icon">📍</div>
                  <div class="no-data-text">暂无周边配套设施信息</div>
                </div>

                <!-- 交通设施 -->
                <div class="facility-category" v-if="nearbyFacilities.transport.length > 0">
                <!-- <div class="category-header">
                  <span class="category-icon">🚇</span>
                  <span class="category-name">交通设施</span>
                </div> -->
                <div class="facility-list">
                  <div
                    class="facility-item clickable"
                    v-for="(item, index) in nearbyFacilities.transport.slice(0, 3)"
                    :key="item.id"
                    @click="focusMapMarker('transport', index)"
                  >
                    <div class="facility-icon transport-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" fill="#4fc3f7" stroke="#fff" stroke-width="2"/>
                        <rect x="6" y="10" width="12" height="4" fill="#fff" rx="2"/>
                        <circle cx="9" cy="12" r="1" fill="#4fc3f7"/>
                        <circle cx="15" cy="12" r="1" fill="#4fc3f7"/>
                      </svg>
                    </div>
                    <div class="facility-name">{{ item.name }}</div>
                    <div class="facility-distance">{{ item.distance }}m</div>
                  </div>
                </div>
              </div>

              <!-- 餐饮配套（统计显示） -->
              <div class="facility-category restaurant-stats">
                <div class="facility-list">
                  <div class="facility-item stats-item">
                    <div class="facility-icon restaurant-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 22 22">
                        <circle cx="11" cy="11" r="9" fill="#ff9800" stroke="#fff" stroke-width="2"/>
                        <rect x="7" y="8" width="8" height="6" fill="#fff" rx="1"/>
                        <circle cx="11" cy="11" r="2" fill="#ff9800"/>
                      </svg>
                    </div>
                    <div class="facility-name">餐饮 {{ restaurantStats.within100m }} 家</div>
                    <div class="facility-distance">100米</div>
                  </div>
                  <div class="facility-item stats-item">
                    <div class="facility-icon restaurant-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 22 22">
                        <circle cx="11" cy="11" r="9" fill="#ff9800" stroke="#fff" stroke-width="2"/>
                        <rect x="7" y="8" width="8" height="6" fill="#fff" rx="1"/>
                        <circle cx="11" cy="11" r="2" fill="#ff9800"/>
                      </svg>
                    </div>
                    <div class="facility-name">餐饮 {{ restaurantStats.within500m }} 家</div>
                    <div class="facility-distance">500米</div>
                  </div>
                </div>
              </div>

              <!-- 银行设施 -->
              <div class="facility-category" v-if="nearbyFacilities.banks.length > 0">
                <div class="facility-list">
                  <div
                    class="facility-item clickable"
                    v-for="(item, index) in nearbyFacilities.banks.slice(0, 5)"
                    :key="item.id"
                    @click="focusMapMarker('banks', index)"
                  >
                    <div class="facility-icon bank-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 22 22">
                        <circle cx="11" cy="11" r="9" fill="#4caf50" stroke="#fff" stroke-width="2"/>
                        <rect x="7" y="8" width="8" height="6" fill="#fff" rx="1"/>
                        <rect x="9" y="6" width="4" height="2" fill="#fff" rx="1"/>
                        <circle cx="11" cy="11" r="1.5" fill="#4caf50"/>
                      </svg>
                    </div>
                    <div class="facility-name">{{ item.name }}</div>
                    <div class="facility-distance">{{ item.distance }}m</div>
                  </div>
                </div>
              </div>

              <!-- 医疗设施 -->
              <div class="facility-category" v-if="nearbyFacilities.medical && nearbyFacilities.medical.length > 0">
                <!-- <div class="category-header">
                  <span class="category-icon">🏥</span>
                  <span class="category-name">医疗设施</span>
                </div> -->
                <div class="facility-list">
                  <div
                    class="facility-item clickable"
                    v-for="(item, index) in nearbyFacilities.medical"
                    :key="item.id"
                    @click="focusMapMarker('medical', index)"
                  >
                    <div class="facility-icon medical-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 22 22">
                        <circle cx="11" cy="11" r="9" fill="#ff6b6b" stroke="#fff" stroke-width="2"/>
                        <rect x="9" y="6" width="4" height="10" fill="#fff" rx="1"/>
                        <rect x="6" y="9" width="10" height="4" fill="#fff" rx="1"/>
                      </svg>
                    </div>
                    <div class="facility-name">{{ item.name }}</div>
                    <div class="facility-distance">{{ item.distance }}m</div>
                  </div>
                </div>
              </div>

              <!-- 调试信息（开发时可见） -->
              <div class="debug-info" v-if="false" style="margin-top: 20px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <div style="color: #4fc3f7; font-size: 12px; margin-bottom: 5px;">调试信息：</div>
                <div style="color: #e3f2fd; font-size: 11px;">
                  交通: {{ (nearbyFacilities.transport && nearbyFacilities.transport.length) || 0 }}个<br>
                  餐饮: 100米内{{ restaurantStats.within100m }}家，500米内{{ restaurantStats.within500m }}家<br>
                  银行: {{ (nearbyFacilities.banks && nearbyFacilities.banks.length) || 0 }}个<br>
                  医疗: {{ (nearbyFacilities.medical && nearbyFacilities.medical.length) || 0 }}个
                </div>
              </div>
              </div> <!-- 关闭配套设施内容div -->
            </div>
          </div>
        </div>

        <!-- 底部数据区域 -->
        <div class="bottom-data-section">
          <!-- GDP与税收总额整体框 -->
          <div class="gdp-tax-container">
            <!-- <div class="container-title">楼宇经济指标</div> -->
            <div class="gdp-tax-content">
              <div class="gdp-tax-item-compact">
                <div class="gdp-tax-title">楼宇GDP</div>
                <div class="gdp-tax-value">
                  {{ formatNumber(buildingInfo.gdp) }}
                  <span class="unit">万元</span>
                </div>
                <div class="gdp-tax-trend">
                  <span class="trend-icon">↗</span>
                  <span class="trend-text">同比增长 12.5%</span>
                </div>
              </div>
              <div class="gdp-tax-item-compact">
                <div class="gdp-tax-title">税收总额</div>
                <div class="gdp-tax-value">
                  {{ formatNumber(buildingInfo.taxRevenue) }}
                  <span class="unit">万元</span>
                </div>
                <div class="gdp-tax-trend">
                  <span class="trend-icon">↗</span>
                  <span class="trend-text">同比增长 8.3%</span>
                </div>
              </div>
            </div>
            <div class="gdp-tax-content">
              <div class="gdp-tax-item-compact">
                <div class="gdp-tax-title">销售均价</div>
                <div class="gdp-tax-value">
                  {{ formatNumber(buildingInfo.soldPriceAmt || 0) }}
                  <span class="unit">元/㎡</span>
                </div>
                <div class="gdp-tax-trend">
                  <span class="trend-icon">↗</span>
                  <span class="trend-text">市场价格</span>
                </div>
              </div>
              <div class="gdp-tax-item-compact">
                <div class="gdp-tax-title">租金均价</div>
                <div class="gdp-tax-value">
                  {{ formatNumber(buildingInfo.avgRent || 0) }}
                  <span class="unit">元/㎡/月</span>
                </div>
                <div class="gdp-tax-trend">
                  <span class="trend-icon">↗</span>
                  <span class="trend-text">租赁价格</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 核心数据指标整体框 -->
          <div class="core-data-container">
            <!-- <div class="container-title">楼宇核心数据</div> -->

            <!-- 静态数据区域 -->
            <div class="static-data-section">
              <div class="static-data-grid">
                <div class="core-data-item-compact">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="车位配比" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ buildingInfo.parkingRatio || '0' }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">车位配比</div>
                </div>

                <div class="core-data-item-compact">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="客梯数量" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ buildingInfo.elevatorCount || '0' }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">客梯数量(部)</div>
                </div>

                <div class="core-data-item-compact">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="物业费" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ buildingInfo.propertyFee || '0' }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">物业费(元/㎡/月)</div>
                </div>

                <div class="core-data-item-compact">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="水电费" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ buildingInfo.utilityCost || '0' }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">水电费(元/度)</div>
                </div>
              </div>
            </div>

            <!-- 动态数据区域 -->
            <div class="dynamic-data-section">
              <div class="dynamic-data-grid">
                <div class="core-data-item-compact dynamic">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="已销售面积" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ formatNumber(buildingInfo.soldArea || 0) }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">已销售面积(㎡)</div>
                </div>

                <div class="core-data-item-compact dynamic">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="已招商面积" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ formatNumber(buildingInfo.leasedArea || 0) }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">已招商面积(㎡)</div>
                </div>

                <div class="core-data-item-compact dynamic">
                  <div class="data-icon-wrapper-compact">
                    <img src="@/assets/icon/btIcon.svg" class="data-icon-compact" alt="待招商面积" />
                    <div class="data-content-compact">
                      <div class="data-value-compact">{{ formatNumber(buildingInfo.availableArea || 0) }}</div>
                    </div>
                  </div>
                  <div class="data-label-compact">待招商面积(㎡)</div>
                </div>

                <div class="core-data-item-compact dynamic vacancy-rate-item">
                  <div class="vacancy-rate-container">
                    <BuildingVacancyRate :vacancy-rate="buildingInfo.vacancyRate" show-label />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="unified-panel unified-right-panel">
        <!-- TOP10企业列表 -->
        <div class="top10-enterprise">
          <div class="panel-title">TOP10企业列表</div>
          <div
            class="ranking-list"
            @mouseenter="stopEnterpriseScroll"
            @mouseleave="startEnterpriseScroll"
          >
            <div v-for="(item, index) in enterpriseTop10" :key="index" class="ranking-item">
              <div
                class="ranking-number"
                :class="'rank-' + (index + 1)"
              >{{ String(index + 1).padStart(2, '0') }}</div>
              <div class="ranking-name">{{ item.name }}</div>
              <div class="ranking-count">{{ formatNumber(item.employees) }}人</div>
            </div>
          </div>
        </div>

        <!-- 企业风险预警 -->
        <div class="enterprise-risk-warning">
          <div class="panel-title-with-filter">
            <div class="panel-title">企业风险预警</div>
            <div class="risk-filter">
              <select v-model="selectedRiskFilter" class="filter-select">
                <option value="all">全部风险</option>
                <option value="拖欠租金">拖欠租金</option>
                <option value="租约到期">租约到期</option>
                <option value="经营风险">经营风险</option>
                <option value="迁出风险">迁出风险</option>
              </select>
            </div>
          </div>
          <div class="warning-list" @mouseenter="stopRiskScroll" @mouseleave="startRiskScroll">
            <div v-for="warning in filteredRiskWarnings" :key="warning.id" class="warning-item">
              <div class="warning-content">
                <div class="warning-company">{{ warning.companyName }}</div>
                <div class="risk-items">
                  <div v-for="risk in warning.risks" :key="risk.type" class="risk-item">
                    <span class="risk-icon" :style="{ color: risk.color }">{{ risk.icon }}</span>
                    <span class="risk-text" :style="{ color: risk.color }">{{ risk.type }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import * as echarts from "echarts";
import BuildingVacancyRate from "@/components/charts/BuildingVacancyRate.vue";
import { getBuildingInfo } from "@/net/buildApi.js";
import { getDistrictsInfo, getStreetsInfo, getBuildingLevels, querySingleBuildingInfo } from "@/net/api.js";
import addressHooksData from "@/net/addressHooks.js";

const route = useRoute();
const router = useRouter();

// 圆形地图容器引用
const circularMapContainer = ref(null);
let circularMap = null;

// 字典数据
const districtsDict = ref([]);
const streetsDict = ref([]);
const buildingLevelsDict = ref([]);

// 移除buildingCoordinates数据库，改用addressHooks.js中的数据

// 图表引用
const capitalChartRef = ref(null);

const buildingInfo = ref({
  id: null,
  name: "",
  district: "",
  street: "",
  star: 0,
  starLevel: "",
  address: "",
  description: "",
  isGreenBuilding: false,
  propertyCompany: "",
  totalCompanies: 0,
  registeredCapital: 0,
  insuredPersonnel: 0,
  jobPositions: 0,
  intellectualProperty: 0,
  // 核心数据指标
  buildingArea: 0,
  totalFloors: 0,
  buildingHeight: 0,
  standardFloorHeight: 0,
  // 经济数据
  soldPriceAmt: 0,
  avgRent: 0,
  // 设施信息
  parkingRatio: "0",
  elevatorCount: 0,
  propertyFee: 0,
  utilityCost: 0,
  // 面积数据
  soldArea: 0,
  leasedArea: 0,
  availableArea: 0,
  vacancyRate: 0,
  // GDP与税收
  gdp: 0,
  taxRevenue: 0
});

// 新增：单个楼宇详细信息数据
const singleBuildingData = ref({
  registryAmt: 0,           // 注册总资本
  insuranceTotal: 0,        // 参保人数
  recruitingTotal: 0,       // 发布招聘岗位
  propertyTotal: 0,         // 知识产权
  registryAmtRing: {},      // 企业注册资本分析
  gdp: 0,                   // 楼宇GDP
  tax: 0,                   // 税收总额
  salesAmtAvg: 0,           // 销售均价
  rentAmtAvg: 0,            // 租金均价
  companyToptenList: [],    // TOP10企业列表
  riskCompanyList: []       // 企业风险预警
});

// 地图相关数据
const buildingMapRef = ref(null);
let buildingMapInstance = null;

// 保存地图标记引用，用于点击列表项时定位
const mapMarkers = ref({
  building: null,
  transport: [],
  commercial: [],
  medical: [],
  banks: []
});

// 周边配套设施数据
const nearbyFacilities = ref({
  transport: [],    // 交通设施
  commercial: [],   // 商业配套（餐饮）
  medical: [],      // 医疗设施
  banks: []         // 银行设施
});

// 餐饮统计数据
const restaurantStats = ref({
  within100m: 0,    // 100米内餐饮数量
  within500m: 0     // 500米内餐饮数量
});

// Loading状态
const mapLoading = ref(false);           // 地图加载状态
const facilitiesLoading = ref(false);    // 配套设施加载状态

// 高德地图API密钥
const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';

// 通过楼宇ID或名称从addressHooks.js中获取坐标信息
const getBuildingCoordinatesById = (buildingId) => {
  console.log('开始获取楼宇坐标，buildingId:', buildingId, '楼宇名称:', buildingInfo.value.name);

  // 优先通过楼宇名称匹配，因为名称更可靠
  const buildingName = buildingInfo.value.name;
  if (buildingName) {
    // 1. 精确匹配楼宇名称
    const exactMatch = addressHooksData.find(item => item.name === buildingName);
    if (exactMatch) {
      console.log(`通过名称精确匹配到楼宇: ${buildingName} -> [${exactMatch.longitude}, ${exactMatch.latitude}]`);
      return [exactMatch.longitude, exactMatch.latitude];
    }

    // 2. 模糊匹配楼宇名称
    const fuzzyMatch = addressHooksData.find(item =>
      item.name.includes(buildingName) ||
      buildingName.includes(item.name)
    );

    if (fuzzyMatch) {
      console.log(`通过名称模糊匹配到楼宇: ${buildingName} -> ${fuzzyMatch.name} -> [${fuzzyMatch.longitude}, ${fuzzyMatch.latitude}]`);
      return [fuzzyMatch.longitude, fuzzyMatch.latitude];
    }
  }

  // 3. 如果有buildingId且不是时间戳格式，尝试通过ID匹配
  if (buildingId && typeof buildingId === 'string' && buildingId.length > 10 && !buildingId.startsWith('16') && !buildingId.startsWith('17')) {
    const building = addressHooksData.find(item => item.id === buildingId);
    if (building) {
      console.log(`通过ID匹配到楼宇: ${building.name} -> [${building.longitude}, ${building.latitude}]`);
      return [building.longitude, building.latitude];
    }
  }

  // 4. 如果都没有匹配到，使用默认坐标
  console.log(`未找到匹配的楼宇坐标，ID: ${buildingId}，名称: ${buildingName}，使用默认坐标`);
  return [112.9823, 28.1941]; // 长沙市中心默认坐标
};

// 通过高德地理编码API获取楼宇坐标
const getCoordinatesByGeocoding = async (buildingName, address) => {
  try {
    const query = address ? `${address} ${buildingName}` : `长沙市 ${buildingName}`;
    const url = `https://restapi.amap.com/v3/geocode/geo?address=${encodeURIComponent(query)}&key=${AMAP_KEY}`;

    console.log('地理编码查询:', query);
    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
      const location = data.geocodes[0].location.split(',');
      const coordinates = [parseFloat(location[0]), parseFloat(location[1])];
      console.log(`地理编码成功: ${buildingName} -> [${coordinates[0]}, ${coordinates[1]}]`);
      return coordinates;
    } else {
      console.warn('地理编码失败:', data);
      return null;
    }
  } catch (error) {
    console.error('地理编码API调用失败:', error);
    return null;
  }
};

// 变更提醒数据
const alertData = ref({
  businessChange: 100,
  businessRisk: 100,
  legalRisk: 100,
  overdueRent: 15,
  migrationAlert: 8
});

// 企业风险预警数据
const enterpriseRiskWarnings = ref([
  {
    id: 1,
    companyName: "华为技术有限公司",
    risks: [
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" },
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" }
    ]
  },
  {
    id: 2,
    companyName: "腾讯科技有限公司",
    risks: [{ type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" }]
  },
  {
    id: 3,
    companyName: "阿里巴巴集团",
    risks: [
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" }
    ]
  },
  {
    id: 4,
    companyName: "百度在线网络",
    risks: [
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" },
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" }
    ]
  },
  {
    id: 5,
    companyName: "字节跳动科技",
    risks: [
      { type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" },
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" }
    ]
  },
  {
    id: 6,
    companyName: "美团点评",
    risks: [
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" }
    ]
  },
  {
    id: 7,
    companyName: "滴滴出行科技",
    risks: [{ type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" }]
  },
  {
    id: 8,
    companyName: "京东集团",
    risks: [{ type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" }]
  },
  {
    id: 9,
    companyName: "网易科技",
    risks: [
      { type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" },
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" }
    ]
  },
  {
    id: 10,
    companyName: "新浪微博",
    risks: [
      { type: "迁出风险", level: "warning", icon: "🚪", color: "#FFA500" }
    ]
  },
  {
    id: 11,
    companyName: "搜狐科技",
    risks: [
      { type: "租约到期", level: "warning", icon: "⚠️", color: "#FFA500" },
      { type: "拖欠租金", level: "danger", icon: "💰", color: "#FF4444" }
    ]
  },
  {
    id: 12,
    companyName: "360科技",
    risks: [{ type: "经营风险", level: "danger", icon: "❗", color: "#FF4444" }]
  }
]);

// TOP10企业列表数据
const enterpriseTop10 = ref([
  { name: "华为技术有限公司", employees: 2800 },
  { name: "腾讯科技有限公司", employees: 2400 },
  { name: "阿里巴巴集团", employees: 2200 },
  { name: "百度在线网络技术", employees: 2000 },
  { name: "字节跳动科技", employees: 1800 },
  { name: "美团点评", employees: 1600 },
  { name: "滴滴出行科技", employees: 1400 },
  { name: "京东集团", employees: 1200 },
  { name: "网易科技", employees: 1000 },
  { name: "新浪微博", employees: 800 },
  { name: "搜狐科技", employees: 600 },
  { name: "360科技", employees: 500 }
]);

// 行业TOP10数据
const industryTop10 = ref([
  { name: "批发和零售业", count: 1123 },
  { name: "租赁和商务服务业", count: 1123 },
  { name: "住宿和餐饮业", count: 1123 },
  { name: "金融业", count: 1123 },
  { name: "制造业", count: 1123 },
  { name: "建筑业", count: 1023 },
  { name: "信息技术服务业", count: 923 },
  { name: "房地产业", count: 823 },
  { name: "交通运输业", count: 723 },
  { name: "教育培训业", count: 623 },
  { name: "医疗健康业", count: 523 },
  { name: "文化娱乐业", count: 423 }
]);

const goBack = () => {
  router.back();
};

// 风险筛选相关
const selectedRiskFilter = ref("all");
const filteredRiskWarnings = ref([]);

// 筛选风险预警数据
const filterRiskWarnings = () => {
  console.log("筛选条件:", selectedRiskFilter.value);
  console.log("原始数据:", enterpriseRiskWarnings.value);

  // 确保数据存在且有效
  if (
    !enterpriseRiskWarnings.value ||
    !Array.isArray(enterpriseRiskWarnings.value)
  ) {
    console.log("数据未准备好");
    filteredRiskWarnings.value = [];
    return;
  }

  if (selectedRiskFilter.value === "all") {
    filteredRiskWarnings.value = [...enterpriseRiskWarnings.value];
  } else {
    filteredRiskWarnings.value = enterpriseRiskWarnings.value.filter(
      warning => {
        // 确保warning和warning.risks存在
        if (!warning || !warning.risks || !Array.isArray(warning.risks)) {
          console.log("警告数据格式错误:", warning);
          return false;
        }
        return warning.risks.some(
          risk => risk && risk.type === selectedRiskFilter.value
        );
      }
    );
  }

  console.log("筛选后数据:", filteredRiskWarnings.value);
};

// 监听筛选条件变化
watch(selectedRiskFilter, () => {
  filterRiskWarnings();
});

// 监听原始数据变化，确保数据加载后重新筛选
watch(
  enterpriseRiskWarnings,
  () => {
    filterRiskWarnings();
  },
  { immediate: true, deep: true }
);

// 初始化圆形地图
const initCircularMap = () => {
  if (!circularMapContainer.value) return;

  // 获取当前楼宇的坐标
  const buildingId = buildingInfo.value.id;
  const coordinates = getBuildingCoordinatesById(buildingId);

  // 创建地图实例
  circularMap = new AMap.Map(circularMapContainer.value, {
    zoom: 16,
    center: coordinates,
    mapStyle: "amap://styles/dark", // 使用深色主题
    showLabel: true, // 显示标签
    showBuildingBlock: false,
    showIndoorMap: false,
    pitch: 0,
    rotation: 0,
    viewMode: "2D",
    features: ["road", "point", "bg"], // 显示道路、兴趣点和背景
    resizeEnable: false,
    dragEnable: false,
    zoomEnable: false,
    doubleClickZoom: false,
    keyboardEnable: false,
    scrollWheel: false,
    touchZoom: false,
    rotateEnable: false,
    pitchEnable: false
  });

  // 添加楼宇位置标记
  const marker = new AMap.Marker({
    position: coordinates,
    icon: new AMap.Icon({
      size: new AMap.Size(12, 12),
      image:
        "data:image/svg+xml;base64," +
        btoa(`
        <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
          <circle cx="6" cy="6" r="4" fill="#ff6b35" stroke="#fff" stroke-width="1"/>
        </svg>
      `),
      imageSize: new AMap.Size(12, 12)
    }),
    anchor: "center"
  });

  circularMap.add(marker);

  // 添加楼宇名称标签
  const text = new AMap.Text({
    text: buildingName,
    anchor: "bottom-center",
    draggable: false,
    cursor: "pointer",
    position: coordinates,
    offset: new AMap.Pixel(0, -15),
    style: {
      padding: "3px 8px",
      "background-color": "rgba(0, 20, 40, 0.8)",
      border: "1px solid rgba(79, 195, 247, 0.6)",
      "border-radius": "10px",
      color: "#4fc3f7",
      "font-size": "12px",
      "font-weight": "bold",
      "white-space": "nowrap",
      "box-shadow": "0 2px 8px rgba(0, 0, 0, 0.3)"
    }
  });

  circularMap.add(text);

  // 获取并显示街道信息
  const geocoder = new AMap.Geocoder({
    radius: 1000,
    extensions: "all"
  });

  geocoder.getAddress(coordinates, (status, result) => {
    if (status === "complete" && result.regeocode) {
      const addressComponent = result.regeocode.addressComponent;
      const streetInfo =
        addressComponent.township || addressComponent.street || "";

      // 更新街道信息显示
      if (streetInfo) {
        buildingInfo.value.street = streetInfo;

        // 添加街道信息标签
        const streetText = new AMap.Text({
          text: streetInfo,
          anchor: "top-center",
          draggable: false,
          position: coordinates,
          offset: new AMap.Pixel(0, 25),
          style: {
            padding: "2px 6px",
            "background-color": "rgba(0, 30, 60, 0.7)",
            border: "1px solid rgba(79, 195, 247, 0.4)",
            "border-radius": "8px",
            color: "#4fc3f7",
            "font-size": "10px",
            "white-space": "nowrap",
            "box-shadow": "0 1px 4px rgba(0, 0, 0, 0.2)"
          }
        });

        circularMap.add(streetText);
      }
    }
  });
};

// 格式化数字显示
const formatNumber = num => {
  if (!num) return "0";
  return num.toLocaleString();
};

// 判断是否为亿元楼宇（GDP超过1亿元）
const isBillionBuilding = computed(() => {
  const gdp = buildingInfo.value.gdp || 0;
  return gdp >= 100000000; // 1亿元 = 100,000,000
});

// 滚动相关变量
let enterpriseScrollInterval = null;
let industryScrollInterval = null;
let migrationScrollInterval = null;

// 企业列表自动滚动
const startEnterpriseScroll = () => {
  const container = document.querySelector(".top10-enterprise .ranking-list");
  if (!container) return;

  enterpriseScrollInterval = setInterval(() => {
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const currentScrollTop = container.scrollTop;

    if (currentScrollTop + clientHeight >= scrollHeight - 5) {
      container.scrollTop = 0;
    } else {
      container.scrollTop += 1;
    }
  }, 50);
};

// 行业列表自动滚动
const startIndustryScroll = () => {
  const container = document.querySelector(".industry-top10 .ranking-list");
  if (!container) return;

  industryScrollInterval = setInterval(() => {
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const currentScrollTop = container.scrollTop;

    if (currentScrollTop + clientHeight >= scrollHeight - 5) {
      container.scrollTop = 0;
    } else {
      container.scrollTop += 1;
    }
  }, 50);
};

// 企业风险预警自动滚动
const startRiskScroll = () => {
  const container = document.querySelector(
    ".enterprise-risk-warning .warning-list"
  );
  if (!container) return;

  migrationScrollInterval = setInterval(() => {
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    const currentScrollTop = container.scrollTop;

    if (currentScrollTop + clientHeight >= scrollHeight - 5) {
      container.scrollTop = 0;
    } else {
      container.scrollTop += 1;
    }
  }, 50);
};

// 停止滚动
const stopEnterpriseScroll = () => {
  if (enterpriseScrollInterval) {
    clearInterval(enterpriseScrollInterval);
    enterpriseScrollInterval = null;
  }
};

const stopIndustryScroll = () => {
  if (industryScrollInterval) {
    clearInterval(industryScrollInterval);
    industryScrollInterval = null;
  }
};

const stopRiskScroll = () => {
  if (migrationScrollInterval) {
    clearInterval(migrationScrollInterval);
    migrationScrollInterval = null;
  }
};

// 初始化企业注册资本分析图表（圆环饼图）
const initCapitalChart = () => {
  if (!capitalChartRef.value) return;

  const chart = echarts.init(capitalChartRef.value);

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "horizontal",
      bottom: "-4%",
      left: "center",
      textStyle: {
        color: "#81d4fa",
        fontSize: 10
      },
      itemWidth: 8,
      itemHeight: 6,
      itemGap: 10
    },
    series: [
      {
        name: "企业注册资本",
        type: "pie",
        radius: ["45%", "65%"],
        center: ["50%", "55%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "18",
            fontWeight: "bold",
            color: "#4fc3f7"
          }
        },
        labelLine: {
          show: false
        },
        data: generateCapitalChartData()
      }
    ]
  };

  chart.setOption(option);
};

// 生成企业注册资本图表数据
const generateCapitalChartData = () => {
  const colors = [
    { color: "#4fc3f7", shadowColor: "rgba(79, 195, 247, 0.5)" },
    { color: "#66bb6a", shadowColor: "rgba(102, 187, 106, 0.5)" },
    { color: "#ffa726", shadowColor: "rgba(255, 167, 38, 0.5)" },
    { color: "#ab47bc", shadowColor: "rgba(171, 71, 188, 0.5)" },
    { color: "#ef5350", shadowColor: "rgba(239, 83, 80, 0.5)" },
    { color: "#26c6da", shadowColor: "rgba(38, 198, 218, 0.5)" }
  ];

  const registryAmtRing = singleBuildingData.value.registryAmtRing || {};
  // 如果没有API数据，使用默认数据
  if (Object.keys(registryAmtRing).length === 0) {
    return [
      {
        value: 25,
        name: "500万以下",
        itemStyle: {
          color: colors[0].color,
          shadowBlur: 10,
          shadowColor: colors[0].shadowColor
        }
      },
      {
        value: 25,
        name: "500-1000万",
        itemStyle: {
          color: colors[1].color,
          shadowBlur: 10,
          shadowColor: colors[1].shadowColor
        }
      },
      {
        value: 25,
        name: "1001-2000万",
        itemStyle: {
          color: colors[2].color,
          shadowBlur: 10,
          shadowColor: colors[2].shadowColor
        }
      },
      {
        value: 25,
        name: "2000万以上",
        itemStyle: {
          color: colors[3].color,
          shadowBlur: 10,
          shadowColor: colors[3].shadowColor
        }
      }
    ];
  }

  // 使用API数据
  return Object.entries(registryAmtRing).map(([name, value], index) => ({
    value: value,
    name: name,
    itemStyle: {
      color: colors[index % colors.length].color,
      shadowBlur: 10,
      shadowColor: colors[index % colors.length].shadowColor
    }
  }));
};

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initCapitalChart();
  });
};

// 获取字典数据
const fetchDictionaries = async () => {
  try {
    console.log('开始获取字典数据...');

    // 并行获取三个字典
    const [districtsResponse, streetsResponse, levelsResponse] = await Promise.all([
      getDistrictsInfo({}),
      getStreetsInfo({}),
      getBuildingLevels()
    ]);

    // 缓存字典数据
    districtsDict.value = districtsResponse.data || [];
    streetsDict.value = streetsResponse.data || [];
    buildingLevelsDict.value = levelsResponse.data || [];

    console.log('字典数据获取成功:', {
      districts: districtsDict.value,
      streets: streetsDict.value,
      levels: buildingLevelsDict.value
    });

  } catch (err) {
    console.error('获取字典数据失败:', err);
  }
};

// 获取楼宇详情数据
const fetchBuildingDetail = async (buildingNo) => {
  try {
    console.log('开始获取楼宇详情数据，buildingNo:', buildingNo);

    const response = await getBuildingInfo({
      buildingNo: buildingNo
    });

    if (response.code === 200 && response.data) {
      console.log('获取楼宇详情数据成功:', response.data);
      // 根据API返回的数据更新buildingInfo
      const apiData = response.data[0].basicVO;

      // 匹配星级字典
      const levelInfo = buildingLevelsDict.value.find(level =>
        level.code === apiData.buildingLevel
      );
      const starLevelName = levelInfo ? levelInfo.description : `${apiData.buildingLevel}星级`;

      // 匹配区域字典
      const districtInfo = districtsDict.value.find(district =>
        district.districtNo === apiData.districtNo
      );
      const districtName = districtInfo ? districtInfo.districtName : '未知区域';

      // 匹配街道字典
      const streetInfo = streetsDict.value.find(street =>
        street.streetNo === apiData.streetNo
      );
      const streetName = streetInfo ? streetInfo.street : '未知街道';

      // 处理绿色建筑认证
      const isGreenBuilding = apiData.greenBuildingAuth === 'Y';

      // 生成楼宇简介
      const buildingName = apiData.buildingName || buildingInfo.value.name;
      const description = `${buildingName}位于${districtName}${streetName}，交通便利，四通八达，周边拥有完善的商业配套设施，包括购物中心、酒店、餐厅等，为企业提供了便利的办公环境。`;

      // 更新楼宇信息
      buildingInfo.value = {
        ...buildingInfo.value,
        // 基本信息
        name: buildingName || "",
        star: levelInfo ? parseInt(levelInfo.code) : 0,
        starLevel: starLevelName || "",
        district: districtName || "",
        street: streetName || "",
        propertyCompany: apiData.propertyCompany || "",
        address: apiData.address || "",
        description: description || "",
        isGreenBuilding: isGreenBuilding,

        // 建筑基础信息
        buildingArea: apiData.totalArea || 0,
        buildingHeight: apiData.buildingHeight || 0,
        totalFloors: apiData.totalFloorNum || 0,
        standardFloorHeight: apiData.standardFloorHeight || 0,

        // 经济数据
        soldPriceAmt: apiData.soldPriceAmt || 0,
        avgRent: apiData.avgRent || 0,

        // 设施信息
        parkingRatio: apiData.parkingRatio || "0",
        elevatorCount: apiData.guestLifts || 0,
        propertyFee: apiData.propertyFee || 0,
        utilityCost: apiData.waterElectricFee || 0,

        // 面积数据
        soldArea: apiData.soldArea || 0,
        leasedArea: apiData.investedArea || 0,
        availableArea: apiData.investingArea || 0,
        vacancyRate: apiData.vacantRatio || 0,
      };

      console.log('更新后的楼宇信息:', buildingInfo.value);
    } else {
      console.warn('获取楼宇详情数据失败:', response);
    }
  } catch (error) {
    console.error('获取楼宇详情数据出错:', error);
  }
};

// 获取单个楼宇详细信息
const fetchSingleBuildingInfo = async (buildingNo) => {
  try {
    console.log('开始获取单个楼宇详细信息，buildingNo:', buildingNo);

    const response = await querySingleBuildingInfo({
      buildingNo: buildingNo
    });

    if (response.code === 200 && response.data) {
      console.log('获取单个楼宇详细信息成功:', response.data);

      // 更新单个楼宇数据
      singleBuildingData.value = {
        registryAmt: response.data.registryAmt || 0,
        insuranceTotal: response.data.insuranceTotal || 0,
        recruitingTotal: response.data.recruitingTotal || 0,
        propertyTotal: response.data.propertyTotal || 0,
        registryAmtRing: response.data.registryAmtRing || {},
        gdp: response.data.gdp || 0,
        tax: response.data.tax || 0,
        salesAmtAvg: response.data.salesAmtAvg || 0,
        rentAmtAvg: response.data.rentAmtAvg || 0,
        companyToptenList: response.data.companyToptenList || [],
        riskCompanyList: response.data.riskCompanyList || []
      };

      // 更新buildingInfo中的相关字段
      buildingInfo.value.registeredCapital = response.data.registryAmt || 0;
      buildingInfo.value.insuredPersonnel = response.data.insuranceTotal || 0;
      buildingInfo.value.jobPositions = response.data.recruitingTotal || 0;
      buildingInfo.value.intellectualProperty = response.data.propertyTotal || 0;
      buildingInfo.value.gdp = response.data.gdp || 0;
      buildingInfo.value.taxRevenue = response.data.tax || 0;
      buildingInfo.value.avgRent = response.data.rentAmtAvg || buildingInfo.value.avgRent;
      buildingInfo.value.soldPriceAmt = response.data.salesAmtAvg || buildingInfo.value.soldPriceAmt;

      // 更新企业风险预警数据
      if (response.data.riskCompanyList && response.data.riskCompanyList.length > 0) {
        enterpriseRiskWarnings.value = response.data.riskCompanyList.map((item, index) => ({
          id: index + 1,
          companyName: item.companyName || '',
          risks: item.riskItems ? item.riskItems.map(risk => ({
            type: risk,
            level: "warning",
            icon: "⚠️",
            color: "#FFA500"
          })) : []
        }));
      }

      // 更新TOP10企业列表
      if (response.data.companyToptenList && response.data.companyToptenList.length > 0) {
        enterpriseTop10.value = response.data.companyToptenList.map(item => ({
          name: item.companyName || '',
          employees: item.employeeNum || 0,
          companyNo: item.companyNo || ''
        }));
      }

      console.log('更新后的单个楼宇数据:', singleBuildingData.value);

      // 重新初始化图表以显示新数据
      nextTick(() => {
        initCapitalChart();
      });
    } else {
      console.warn('获取单个楼宇详细信息失败:', response);
    }
  } catch (error) {
    console.error('获取单个楼宇详细信息出错:', error);
  }
};

// 点击列表项聚焦地图标记
const focusMapMarker = (type, index) => {
  if (!buildingMapInstance) {
    console.warn('地图实例未初始化');
    return;
  }

  let marker = null;
  let infoWindow = null;

  // 根据类型获取对应的标记
  if (type === 'transport' && mapMarkers.value.transport[index]) {
    marker = mapMarkers.value.transport[index].marker;
    infoWindow = mapMarkers.value.transport[index].infoWindow;
  } else if (type === 'commercial' && mapMarkers.value.commercial[index]) {
    marker = mapMarkers.value.commercial[index].marker;
    infoWindow = mapMarkers.value.commercial[index].infoWindow;
  } else if (type === 'medical' && mapMarkers.value.medical[index]) {
    marker = mapMarkers.value.medical[index].marker;
    infoWindow = mapMarkers.value.medical[index].infoWindow;
  } else if (type === 'banks' && mapMarkers.value.banks && mapMarkers.value.banks[index]) {
    marker = mapMarkers.value.banks[index].marker;
    infoWindow = mapMarkers.value.banks[index].infoWindow;
  }

  if (marker && infoWindow) {
    // 聚焦到标记位置
    buildingMapInstance.setCenter(marker.getPosition());
    buildingMapInstance.setZoom(17); // 放大一点以便更好地查看

    // 显示信息窗体
    infoWindow.open(buildingMapInstance, marker.getPosition());

    console.log(`聚焦到${type}标记，索引:${index}`);
  } else {
    console.warn(`未找到${type}类型的标记，索引:${index}`);
  }
};

// 搜索周边配套设施
const searchNearbyFacilities = async (longitude, latitude) => {
  facilitiesLoading.value = true; // 开始加载

  try {
    console.log('开始搜索周边配套设施，坐标:', longitude, latitude);

    // 初始化所有配套设施数组
    nearbyFacilities.value.transport = [];
    nearbyFacilities.value.commercial = [];
    nearbyFacilities.value.medical = [];
    nearbyFacilities.value.banks = [];
    restaurantStats.value = { within100m: 0, within500m: 0 };

    // 搜索交通设施（地铁站、公交站）
    const transportUrl = `https://restapi.amap.com/v3/place/around?location=${longitude},${latitude}&radius=1000&types=150500|150600&key=${AMAP_KEY}&extensions=all`;
    console.log('交通设施搜索URL:', transportUrl);
    const transportResponse = await fetch(transportUrl);
    const transportData = await transportResponse.json();
    console.log('交通设施搜索结果:', transportData);

    // 搜索餐饮设施（500米内）
    const restaurantUrl = `https://restapi.amap.com/v3/place/around?location=${longitude},${latitude}&radius=500&types=050000&key=${AMAP_KEY}&extensions=all`;
    console.log('餐饮设施搜索URL:', restaurantUrl);
    const restaurantResponse = await fetch(restaurantUrl);
    const restaurantData = await restaurantResponse.json();
    console.log('餐饮设施搜索结果:', restaurantData);

    // 搜索银行设施（只搜索银行，不包括其他金融机构）
    const bankUrl = `https://restapi.amap.com/v3/place/around?location=${longitude},${latitude}&radius=1000&types=160100&key=${AMAP_KEY}&extensions=all`;
    console.log('银行设施搜索URL:', bankUrl);
    const bankResponse = await fetch(bankUrl);
    const bankData = await bankResponse.json();
    console.log('银行设施搜索结果:', bankData);

    // 搜索医疗设施
    const medicalUrl = `https://restapi.amap.com/v3/place/around?location=${longitude},${latitude}&radius=2000&types=090000&key=${AMAP_KEY}&extensions=all`;
    console.log('医疗设施搜索URL:', medicalUrl);
    const medicalResponse = await fetch(medicalUrl);
    const medicalData = await medicalResponse.json();
    console.log('医疗设施搜索结果:', medicalData);

    // 处理交通设施数据（只取2个地铁站）
    if (transportData.status === '1' && transportData.pois) {
      // 优先选择地铁站，然后是公交站
      const subwayStations = transportData.pois.filter(poi => poi.type.includes('地铁') || poi.typecode === '150600');
      const busStations = transportData.pois.filter(poi => poi.type.includes('公交') || poi.typecode === '150500');

      let selectedTransport = [];
      // 先取2个地铁站
      selectedTransport = selectedTransport.concat(subwayStations.slice(0, 2));
      // 如果地铁站不足2个，用公交站补充
      if (selectedTransport.length < 2) {
        selectedTransport = selectedTransport.concat(busStations.slice(0, 2 - selectedTransport.length));
      }

      nearbyFacilities.value.transport = selectedTransport.map(poi => ({
        id: poi.id,
        name: poi.name,
        type: poi.type,
        location: poi.location.split(',').map(Number),
        distance: parseInt(poi.distance),
        address: poi.address
      }));
      console.log('交通设施处理完成，数量:', nearbyFacilities.value.transport.length);
    } else {
      console.warn('交通设施搜索失败或无结果:', transportData);
      nearbyFacilities.value.transport = [];
    }

    // 处理餐饮数据（统计100米和500米内的数量）
    if (restaurantData.status === '1' && restaurantData.pois) {
      const restaurants = restaurantData.pois.filter(poi =>
        poi.type.includes('餐') || poi.type.includes('饭') || poi.typecode.startsWith('050')
      );

      // 统计100米和500米内的餐饮数量
      const within100m = restaurants.filter(poi => parseInt(poi.distance) <= 100).length;
      const within500m = restaurants.length;

      restaurantStats.value = {
        within100m: within100m,
        within500m: within500m
      };

      // 不再存储具体的餐饮名称，只保留统计数据
      nearbyFacilities.value.commercial = [];
      console.log('餐饮统计完成:', restaurantStats.value);
    } else {
      console.warn('餐饮搜索失败或无结果:', restaurantData);
      restaurantStats.value = { within100m: 0, within500m: 0 };
      nearbyFacilities.value.commercial = [];
    }

    // 处理银行数据（显示具体银行名称和距离）
    if (bankData.status === '1' && bankData.pois) {
      const banks = bankData.pois.filter(poi =>
        poi.type.includes('银行') || poi.typecode === '160100'
      );

      nearbyFacilities.value.banks = banks.slice(0, 5).map(poi => ({
        id: poi.id,
        name: poi.name,
        type: poi.type,
        location: poi.location.split(',').map(Number),
        distance: parseInt(poi.distance),
        address: poi.address
      }));
      console.log('银行设施处理完成，数量:', nearbyFacilities.value.banks.length);
    } else {
      console.warn('银行搜索失败或无结果:', bankData);
      nearbyFacilities.value.banks = [];
    }

    // 处理医疗设施数据（只取1家医院）
    if (medicalData.status === '1' && medicalData.pois) {
      // 优先选择医院
      const hospitals = medicalData.pois.filter(poi =>
        poi.type.includes('医院') || poi.typecode === '090100'
      );

      nearbyFacilities.value.medical = hospitals.slice(0, 1).map(poi => ({
        id: poi.id,
        name: poi.name,
        type: poi.type,
        location: poi.location.split(',').map(Number),
        distance: parseInt(poi.distance),
        address: poi.address
      }));
      console.log('医疗设施处理完成，数量:', nearbyFacilities.value.medical.length);
    } else {
      console.warn('医疗设施搜索失败或无结果:', medicalData);
      nearbyFacilities.value.medical = [];
    }

    console.log('周边配套设施搜索完成:', {
      transport: nearbyFacilities.value.transport.length,
      restaurants: restaurantStats.value,
      banks: nearbyFacilities.value.banks.length,
      medical: nearbyFacilities.value.medical.length
    });

  } catch (error) {
    console.error('搜索周边配套设施失败:', error);
  } finally {
    facilitiesLoading.value = false; // 结束加载
  }
};

// 初始化楼宇地图
const initBuildingMap = async (longitude, latitude) => {
  if (!buildingMapRef.value) {
    console.error('地图容器未找到');
    return;
  }

  mapLoading.value = true; // 开始加载地图
  console.log('开始初始化楼宇地图，坐标:', longitude, latitude);

  // 先搜索周边设施
  await searchNearbyFacilities(longitude, latitude);

  // 等待下一个tick确保数据更新
  await nextTick();

  try {
    // 创建高德地图实例
    buildingMapInstance = new AMap.Map(buildingMapRef.value, {
      center: [longitude, latitude],
      zoom: 16,
      mapStyle: 'amap://styles/dark',
      viewMode: '2D',
      resizeEnable: true,
      rotateEnable: false,
      pitchEnable: false,
      zoomEnable: true,
      dragEnable: true
    });

    console.log('地图实例创建成功');

    // 清空之前的标记引用
    mapMarkers.value = {
      building: null,
      transport: [],
      commercial: [],
      medical: []
    };

    // 等待地图加载完成
    buildingMapInstance.on('complete', () => {
      console.log('地图加载完成，开始添加标记');

      // 清空所有标记数组
      mapMarkers.value.transport = [];
      mapMarkers.value.commercial = [];
      mapMarkers.value.medical = [];
      mapMarkers.value.banks = [];

      // 添加楼宇标记（金色，更大更突出）
      const buildingMarker = new AMap.Marker({
        position: [longitude, latitude],
        title: buildingInfo.value.name || '当前楼宇',
        icon: new AMap.Icon({
          size: new AMap.Size(48, 48),
          image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
              <!-- 外圈发光效果 -->
              <circle cx="24" cy="24" r="22" fill="#ffd700" opacity="0.3"/>
              <circle cx="24" cy="24" r="20" fill="#ffd700" stroke="#fff" stroke-width="3"/>
              <!-- 楼宇建筑 -->
              <rect x="14" y="10" width="20" height="28" fill="#fff" rx="2"/>
              <!-- 窗户 -->
              <rect x="17" y="13" width="3" height="3" fill="#ffd700"/>
              <rect x="22" y="13" width="3" height="3" fill="#ffd700"/>
              <rect x="27" y="13" width="3" height="3" fill="#ffd700"/>
              <rect x="17" y="18" width="3" height="3" fill="#ffd700"/>
              <rect x="22" y="18" width="3" height="3" fill="#ffd700"/>
              <rect x="27" y="18" width="3" height="3" fill="#ffd700"/>
              <rect x="17" y="23" width="3" height="3" fill="#ffd700"/>
              <rect x="22" y="23" width="3" height="3" fill="#ffd700"/>
              <rect x="27" y="23" width="3" height="3" fill="#ffd700"/>
              <rect x="17" y="28" width="3" height="3" fill="#ffd700"/>
              <rect x="22" y="28" width="3" height="3" fill="#ffd700"/>
              <rect x="27" y="28" width="3" height="3" fill="#ffd700"/>
              <!-- 大门 -->
              <rect x="21" y="32" width="6" height="6" fill="#ffd700"/>
              <!-- 顶部标识 -->
              <text x="24" y="8" text-anchor="middle" fill="#ffd700" font-size="8" font-weight="bold">★</text>
            </svg>
          `),
          imageSize: new AMap.Size(48, 48)
        })
      });

      // 为楼宇标记添加信息窗体
      const buildingInfoWindow = new AMap.InfoWindow({
        content: `
          <div style="padding: 10px; min-width: 200px;">
            <div style="font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;">
              ${buildingInfo.value.name || '当前楼宇'}
            </div>
            <div style="color: #666; font-size: 12px; line-height: 1.5;">
              <div>📍 ${buildingInfo.value.address || '地址信息'}</div>
              <div>⭐ ${buildingInfo.value.starLevel || '星级信息'}</div>
              <div>📞 ${buildingInfo.value.propertyCompany || '物业公司信息'}</div>
            </div>
          </div>
        `,
        offset: new AMap.Pixel(35, -15)
      });

      // 添加点击事件
      buildingMarker.on('click', () => {
        buildingInfoWindow.open(buildingMapInstance, buildingMarker.getPosition());
      });

      buildingMapInstance.add(buildingMarker);

      // 保存楼宇标记引用
      mapMarkers.value.building = {
        marker: buildingMarker,
        infoWindow: buildingInfoWindow
      };

      console.log('楼宇标记添加成功');

      // 添加交通设施标记（蓝色）
      console.log('交通设施数量:', nearbyFacilities.value.transport.length);
      nearbyFacilities.value.transport.forEach((item, index) => {
        const marker = new AMap.Marker({
          position: item.location,
          title: `${item.name} (${item.distance}m)`,
          icon: new AMap.Icon({
            size: new AMap.Size(24, 24),
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" fill="#4fc3f7" stroke="#fff" stroke-width="2"/>
                <rect x="6" y="10" width="12" height="4" fill="#fff" rx="2"/>
                <circle cx="9" cy="12" r="1" fill="#4fc3f7"/>
                <circle cx="15" cy="12" r="1" fill="#4fc3f7"/>
              </svg>
            `),
            imageSize: new AMap.Size(24, 24)
          })
        });

        // 为交通设施添加信息窗体
        const infoWindow = new AMap.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 180px;">
              <div style="font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;">
                🚇 ${item.name}
              </div>
              <div style="color: #666; font-size: 12px; line-height: 1.5;">
                <div>📍 ${item.address || '地址信息暂无'}</div>
                <div>📏 距离: ${item.distance}米</div>
                <div>🚌 类型: ${item.type}</div>
                <div>📞 咨询电话: 12306</div>
              </div>
            </div>
          `,
          offset: new AMap.Pixel(10, -5)
        });

        // 添加点击事件
        marker.on('click', () => {
          infoWindow.open(buildingMapInstance, marker.getPosition());
        });

        buildingMapInstance.add(marker);

        // 保存交通设施标记引用
        mapMarkers.value.transport.push({
          marker: marker,
          infoWindow: infoWindow,
          data: item
        });

        console.log(`交通设施标记 ${index + 1} 添加成功:`, item.name);
      });

      // 添加商业配套标记（青色）
      console.log('商业配套数量:', nearbyFacilities.value.commercial.length);
      nearbyFacilities.value.commercial.forEach((item, index) => {
        const marker = new AMap.Marker({
          position: item.location,
          title: `${item.name} (${item.distance}m)`,
          icon: new AMap.Icon({
            size: new AMap.Size(22, 22),
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
              <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22">
                <circle cx="11" cy="11" r="9" fill="#26d0ce" stroke="#fff" stroke-width="2"/>
                <rect x="6" y="7" width="10" height="8" fill="#fff" rx="1"/>
                <rect x="8" y="9" width="2" height="2" fill="#26d0ce"/>
                <rect x="12" y="9" width="2" height="2" fill="#26d0ce"/>
                <rect x="8" y="11" width="6" height="1" fill="#26d0ce"/>
              </svg>
            `),
            imageSize: new AMap.Size(22, 22)
          })
        });

        // 为商业配套添加信息窗体
        const infoWindow = new AMap.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 180px;">
              <div style="font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;">
                � ${item.name}
              </div>
              <div style="color: #666; font-size: 12px; line-height: 1.5;">
                <div>📍 ${item.address || '地址信息暂无'}</div>
                <div>📏 距离: ${item.distance}米</div>
                <div>🍽️ 类型: ${item.type}</div>
                <div>📞 联系电话: 400-000-0000</div>
                <div>⏰ 营业时间: 09:00-22:00</div>
              </div>
            </div>
          `,
          offset: new AMap.Pixel(12, -5)
        });

        // 添加点击事件
        marker.on('click', () => {
          infoWindow.open(buildingMapInstance, marker.getPosition());
        });

        buildingMapInstance.add(marker);

        // 保存商业配套标记引用
        mapMarkers.value.commercial.push({
          marker: marker,
          infoWindow: infoWindow,
          data: item
        });

        console.log(`商业配套标记 ${index + 1} 添加成功:`, item.name);
      });

      // 添加医疗设施标记（红色）
      console.log('医疗设施数量:', nearbyFacilities.value.medical.length);
      nearbyFacilities.value.medical.forEach((item, index) => {
        const marker = new AMap.Marker({
          position: item.location,
          title: `${item.name} (${item.distance}m)`,
          icon: new AMap.Icon({
            size: new AMap.Size(22, 22),
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
              <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22">
                <circle cx="11" cy="11" r="9" fill="#ff6b6b" stroke="#fff" stroke-width="2"/>
                <rect x="9" y="6" width="4" height="10" fill="#fff" rx="1"/>
                <rect x="6" y="9" width="10" height="4" fill="#fff" rx="1"/>
              </svg>
            `),
            imageSize: new AMap.Size(22, 22)
          })
        });

        // 为医疗设施添加信息窗体
        const infoWindow = new AMap.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 180px;">
              <div style="font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;">
                🏥 ${item.name}
              </div>
              <div style="color: #666; font-size: 12px; line-height: 1.5;">
                <div>📍 ${item.address || '地址信息暂无'}</div>
                <div>📏 距离: ${item.distance}米</div>
                <div>🏥 类型: ${item.type}</div>
                <div>📞 预约电话: 0731-12345678</div>
                <div>⏰ 门诊时间: 08:00-17:30</div>
                <div>🚑 急诊: 24小时</div>
              </div>
            </div>
          `,
          offset: new AMap.Pixel(3, -25)
        });

        // 添加点击事件
        marker.on('click', () => {
          infoWindow.open(buildingMapInstance, marker.getPosition());
        });

        buildingMapInstance.add(marker);

        // 保存医疗设施标记引用
        mapMarkers.value.medical.push({
          marker: marker,
          infoWindow: infoWindow,
          data: item
        });

        console.log(`医疗设施标记 ${index + 1} 添加成功:`, item.name);
      });

      // 添加银行设施标记（绿色）
      console.log('银行设施数量:', nearbyFacilities.value.banks.length);
      // 确保银行标记数组已初始化
      if (!mapMarkers.value.banks) {
        mapMarkers.value.banks = [];
      }
      nearbyFacilities.value.banks.forEach((item, index) => {
        const marker = new AMap.Marker({
          position: item.location,
          title: `${item.name} (${item.distance}m)`,
          icon: new AMap.Icon({
            size: new AMap.Size(22, 22),
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
              <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22">
                <circle cx="11" cy="11" r="9" fill="#4caf50" stroke="#fff" stroke-width="2"/>
                <rect x="7" y="8" width="8" height="6" fill="#fff" rx="1"/>
                <rect x="9" y="6" width="4" height="2" fill="#fff" rx="1"/>
                <circle cx="11" cy="11" r="1.5" fill="#4caf50"/>
              </svg>
            `),
            imageSize: new AMap.Size(22, 22)
          })
        });

        // 为银行设施添加信息窗体
        const infoWindow = new AMap.InfoWindow({
          content: `
            <div style="padding: 10px; min-width: 180px;">
              <div style="font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;">
                🏦 ${item.name}
              </div>
              <div style="color: #666; font-size: 12px; line-height: 1.5;">
                <div>📍 ${item.address || '地址信息暂无'}</div>
                <div>📏 距离: ${item.distance}米</div>
                <div>🏦 类型: ${item.type}</div>
                <div>📞 客服电话: 95588</div>
                <div>⏰ 营业时间: 09:00-17:00</div>
                <div>💳 ATM: 24小时服务</div>
              </div>
            </div>
          `,
          offset: new AMap.Pixel(3, -25)
        });

        // 添加点击事件
        marker.on('click', () => {
          infoWindow.open(buildingMapInstance, marker.getPosition());
        });

        buildingMapInstance.add(marker);

        // 保存银行设施标记引用
        mapMarkers.value.banks.push({
          marker: marker,
          infoWindow: infoWindow,
          data: item
        });

        console.log(`银行设施标记 ${index + 1} 添加成功:`, item.name);
      });

      console.log('所有标记添加完成');

      // 地图和标记都加载完成，关闭loading
      mapLoading.value = false;
    });

    console.log('楼宇地图初始化完成');

  } catch (error) {
    console.error('初始化楼宇地图失败:', error);
    mapLoading.value = false; // 出错时也要关闭loading
  }
};

onMounted(async () => {
  try {
    console.log("楼宇详情页路由信息:", route);

    const {
      buildingId,
      buildingNo,
      buildingName,
      district,
      street,
      star,
      address
    } = route.query;

    console.log("获取到的楼宇参数:", {
      buildingId,
      buildingNo,
      buildingName,
      district,
      street,
      star,
      address
    });

    // 设置默认楼宇信息
    buildingInfo.value = {
      id: buildingId || Date.now(),
      name: buildingName || "",
      district: district || "",
      street: street || "",
      propertyCompany: "",
      star: parseInt(star) || 0,
      starLevel: "",
      address: address || "",
      description: "",
      isGreenBuilding: false,
      totalCompanies: 0,
      registeredCapital: 0,
      insuredPersonnel: 0,
      jobPositions: 0,
      intellectualProperty: 0,
      // 核心数据指标
      buildingArea: 0,
      totalFloors: 0,
      buildingHeight: 0,
      standardFloorHeight: 0,
      // 经济数据
      soldPriceAmt: 0,
      avgRent: 0,
      // 设施信息
      parkingRatio: "0",
      elevatorCount: 0,
      propertyFee: 0,
      utilityCost: 0,
      // 面积数据
      soldArea: 0,
      leasedArea: 0,
      availableArea: 0,
      vacancyRate: 0,
      // GDP与税收
      gdp: 0,
      taxRevenue: 0
    };

    console.log("设置的默认楼宇信息:", buildingInfo.value);

    // 先获取字典数据
    await fetchDictionaries();

    // 如果有buildingNo，调用API获取真实数据
    if (buildingNo) {
      await fetchBuildingDetail(buildingNo);
      // 获取单个楼宇详细信息
      await fetchSingleBuildingInfo(buildingNo);
    }

    // 初始化筛选数据
    filterRiskWarnings();

    // 初始化图表
    initCharts();

    // 初始化圆形地图
    nextTick(() => {
      initCircularMap();
    });

    // 初始化楼宇地图和周边设施
    nextTick(async () => {
      // 根据当前楼宇ID获取真实坐标
      const buildingId = buildingInfo.value.id;
      const buildingName = buildingInfo.value.name;
      const buildingAddress = buildingInfo.value.address;

      let coordinates = getBuildingCoordinatesById(buildingId);

      // 如果没有通过ID匹配到坐标，且楼宇名称存在，尝试地理编码API
      const defaultCoords = [112.9823, 28.1941];
      if (coordinates[0] === defaultCoords[0] && coordinates[1] === defaultCoords[1] && buildingName && buildingName !== "") {
        console.log('尝试通过地理编码API获取楼宇坐标...');
        const geocodedCoords = await getCoordinatesByGeocoding(buildingName, buildingAddress);
        if (geocodedCoords) {
          coordinates = geocodedCoords;
        }
      }

      const longitude = coordinates[0];
      const latitude = coordinates[1];

      console.log(`当前楼宇: ${buildingName}, 最终坐标: [${longitude}, ${latitude}]`);

      try {
        await initBuildingMap(longitude, latitude);
      } catch (error) {
        console.error('初始化楼宇地图失败:', error);
      }
    });

    // 延迟启动自动滚动
    setTimeout(() => {
      startEnterpriseScroll();
      startIndustryScroll();
      startRiskScroll();
    }, 2000);
  } catch (error) {
    console.error("初始化楼宇信息失败:", error);
  }
});

onUnmounted(() => {
  stopEnterpriseScroll();
  stopIndustryScroll();
  stopRiskScroll();

  // 清理地图实例
  if (buildingMapInstance) {
    buildingMapInstance.destroy();
    buildingMapInstance = null;
  }

  // 清理标记引用
  mapMarkers.value = {
    building: null,
    transport: [],
    commercial: [],
    medical: []
  };
});
</script>

<style scoped lang="less">
#buildingBox {
  width: 100vw;
  height: 100%;
  background: url(../assets/img/bg4.jpg);
  background-size: cover;
  background-attachment: fixed;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.header-section {
  padding: 10px 30px;
  position: relative;
  z-index: 100;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4fc3f7;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 6px;
  background: rgba(79, 195, 247, 0.1);
  font-size: 14px;
  margin-bottom: 15px;
}

.back-button:hover {
  background: rgba(79, 195, 247, 0.2);
  border-color: #4fc3f7;
  transform: translateX(-2px);
}

.building-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.building-info-left {
  flex: 1;
}

/* 圆形地图定位样式 */
.circular-map {
  position: absolute;
  width: 240px;
  height: 240px;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-container {
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  background: rgba(0, 20, 40, 0.2);
  backdrop-filter: blur(3px);
  z-index: 2;
}



/* dv-decoration-9 样式调整 */
.circular-map .map-decoration {
  position: relative;
  z-index: 1;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circular-map .map-decoration svg {
  filter: drop-shadow(0 0 10px rgba(79, 195, 247, 0.3));
  border-radius: 50%;
}

/* 确保装饰组件内容为圆形 */
.circular-map .map-decoration > div {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 30% 30%,
    rgba(79, 195, 247, 0.1),
    transparent 50%
  );
}

.map-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.grid-line {
  position: absolute;
  background: rgba(79, 195, 247, 0.2);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
}

.grid-line.horizontal:nth-child(1) {
  top: 20%;
}
.grid-line.horizontal:nth-child(2) {
  top: 40%;
}
.grid-line.horizontal:nth-child(3) {
  top: 60%;
}
.grid-line.horizontal:nth-child(4) {
  top: 80%;
}

.grid-line.vertical {
  height: 100%;
  width: 1px;
}

.grid-line.vertical:nth-child(6) {
  left: 20%;
}
.grid-line.vertical:nth-child(7) {
  left: 40%;
}
.grid-line.vertical:nth-child(8) {
  left: 60%;
}
.grid-line.vertical:nth-child(9) {
  left: 80%;
}

.location-marker {
  position: absolute;
}

.main-marker {
  top: 45%;
  left: 45%;
  transform: translate(-50%, -50%);
}

.marker-dot {
  width: 8px;
  height: 8px;
  background: #ff6b35;
  border-radius: 50%;
  position: relative;
  z-index: 2;
}

.marker-dot.small {
  width: 4px;
  height: 4px;
  background: #4fc3f7;
}

.marker-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: rgba(255, 107, 53, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.direction-indicator {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
}

.arrow-up {
  color: #4fc3f7;
  font-size: 12px;
  text-shadow: 0 0 5px rgba(79, 195, 247, 0.5);
}

.district-label {
  position: absolute;
  bottom: -10px;
  left: 30%;
  transform: translateX(-50%);
  background: rgba(0, 20, 40, 0.8);
  color: #4fc3f7;
  padding: 6px 16px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid rgba(79, 195, 247, 0.4);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.building-title-row {
  display: flex;
  align-items: center;
}

.building-title {
  font-size: 32px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 20px rgba(79, 195, 247, 0.8);
  letter-spacing: 2px;
  margin-bottom: 10px;
}

.building-badges {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 10px;
}

.star-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  min-width: 50px;
}

/* 波光效果背景 */
.star-rating::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 根据星级动态设置颜色 */
.star-rating.star-4 {
  background: rgba(255, 112, 67, 0.15);
  border: 1px solid rgba(255, 112, 67, 0.4);
}

.star-rating.star-4 .star-text {
  color: #ff7043;
  text-shadow: 0 0 8px rgba(255, 112, 67, 0.6);
}

.star-rating.star-5 {
  background: rgba(255, 167, 38, 0.15);
  border: 1px solid rgba(255, 167, 38, 0.4);
}

.star-rating.star-5 .star-text {
  color: #ffa726;
  text-shadow: 0 0 8px rgba(255, 167, 38, 0.6);
}

.star-rating.star-6 {
  background: rgba(102, 187, 106, 0.15);
  border: 1px solid rgba(102, 187, 106, 0.4);
}

.star-rating.star-6 .star-text {
  color: #66bb6a;
  text-shadow: 0 0 8px rgba(102, 187, 106, 0.6);
}

.star-rating.star-7 {
  background: rgba(79, 195, 247, 0.15);
  border: 1px solid rgba(79, 195, 247, 0.4);
}

.star-rating.star-7 .star-text {
  color: #4fc3f7;
  text-shadow: 0 0 8px rgba(79, 195, 247, 0.6);
}

.star-text {
  position: relative;
  z-index: 2;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.green-building {
  background: rgba(76, 175, 80, 0.15);
  border: 1px solid rgba(76, 175, 80, 0.4);
  padding: 4px 8px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.green-building::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
  z-index: 1;
}

.green-text {
  position: relative;
  z-index: 2;
  color: #4caf50;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.star-rating.star-7 {
  background: rgba(79, 195, 247, 0.15);
  border: 1px solid rgba(79, 195, 247, 0.4);
}

.star-rating.star-7 .star-text {
  color: #4fc3f7;
  text-shadow: 0 0 8px rgba(79, 195, 247, 0.6);
}

.star-text {
  font-size: 13px;
  font-weight: bold;
  position: relative;
  z-index: 2;
  white-space: nowrap;
  letter-spacing: 0.5px;
}

.green-building {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(76, 175, 80, 0.15);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(76, 175, 80, 0.4);
  position: relative;
  overflow: hidden;
  min-width: 70px;
}

/* 绿色建筑波光效果 */
.green-building::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
  z-index: 1;
}

.green-text {
  color: #4caf50;
  font-size: 13px;
  font-weight: bold;
  position: relative;
  z-index: 2;
  white-space: nowrap;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

/* 亿元楼宇标签样式 */
.billion-building {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 193, 7, 0.15);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(255, 193, 7, 0.4);
  position: relative;
  overflow: hidden;
  min-width: 70px;
}

/* 亿元楼宇波光效果 */
.billion-building::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
  z-index: 1;
}

.billion-text {
  color: #ffc107;
  font-size: 12px;
  font-weight: bold;
  position: relative;
  z-index: 2;
  white-space: nowrap;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
}

.building-location {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  color: #81d4fa;
  font-size: 14px;
  line-height: 1.5;
}

.building-location > span {
  white-space: nowrap;
}

.location-address {
  color: #4fc3f7;
  font-weight: 500;
  flex-basis: 100%;
  margin-top: 5px;
}

.location-icon {
  font-size: 16px;
}

.location-address {
  color: #e3f2fd;
}

.building-intro {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #4fc3f7;
  width: 70%;
}

.intro-text {
  color: #e3f2fd;
  font-size: 14px;
  line-height: 1.6;
}

.intro-label {
  color: #4fc3f7;
  font-weight: 600;
}

.expand-btn {
  color: #ffa726;
  cursor: pointer;
  margin-left: 10px;
  font-weight: 500;
}

.expand-btn:hover {
  color: #ffb74d;
}

/* 变更提醒区域 */
.alert-section {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  padding: 10px 30px;
  margin: 10px 30px 10px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #e3f2fd;
  font-size: 14px;
}

.alert-icon {
  font-size: 18px;
}

.alert-text {
  color: #f44336;
  font-weight: 600;
}

.alert-detail {
  color: #e3f2fd;
}

.highlight {
  color: #ffa726;
  font-weight: 600;
}

/* 移除冲突的main-content样式，使用统一布局 */

/* 使用统一布局，移除left-panel和right-panel样式 */

/* 左侧分析图表 */
.analysis-charts-left {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.chart-item-left {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
}

.chart-container-left {
  height: 160px;
  width: 100%;
}

/* 建筑基础信息样式 */
.building-basic-info {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.basic-info-grid {
  display: flex;
  flex-direction: column;
}

.basic-info-grid .stats-row {
  display: flex;
  gap: 20px;
}

.basic-info-item {
  flex: 1;
  text-align: center;
  padding: 5px 15px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(79, 195, 247, 0.2);
}

.basic-info-item .info-label {
  color: #81d4fa;
  font-size: 12px;
  margin-bottom: 8px;
}

.basic-info-item .info-value {
  color: #e3f2fd;
  font-size: 18px;
  font-weight: bold;
}

.basic-info-item .unit {
  font-size: 12px;
  color: #81d4fa;
  margin-left: 5px;
}

.center-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 楼宇地图和配套设施区域 */
.building-map-facilities-area {
  flex: 1;
  display: flex;
  gap: 15px;
  border-radius: 12px;
  overflow: hidden;
  min-height: 400px;
}

/* 左侧地图区域 */
.map-section {
  flex: 1.2;
  background: rgba(0, 20, 50, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

.map-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-title {
  padding: 15px 20px 10px;
  color: #4fc3f7;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.building-map {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 350px;
  border-radius: 0; /* 确保方形 */
}

/* 右侧配套设施区域 */
.facilities-section {
  flex: 0.8;
  background: rgba(0, 20, 50, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

.facilities-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.facilities-container::-webkit-scrollbar {
  width: 6px;
}

.facilities-container::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 3px;
}

.facilities-container::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.3);
  border-radius: 3px;
}

.facilities-container::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.5);
}

.facilities-title {
  color: #4fc3f7;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

/* 配套设施分类样式 */
.facility-category {
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 6px;
  border-left: 3px solid #4fc3f7;
}

.category-icon {
  font-size: 16px;
  margin-right: 8px;
}

.category-name {
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
}

.facility-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.facility-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(79, 195, 247, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(79, 195, 247, 0.1);
  transition: all 0.3s ease;
}

.facility-item:hover {
  background: rgba(79, 195, 247, 0.1);
  border-color: rgba(79, 195, 247, 0.3);
}

.facility-item.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.facility-item.clickable:hover {
  background: rgba(79, 195, 247, 0.15);
  border-color: rgba(79, 195, 247, 0.4);
  transform: translateX(3px);
  box-shadow: 0 2px 8px rgba(79, 195, 247, 0.2);
}

.facility-item.clickable:active {
  transform: translateX(1px);
  background: rgba(79, 195, 247, 0.2);
}

/* 餐饮统计样式 */
.restaurant-stats .facility-item {
  background: rgba(255, 167, 38, 0.05);
  border-color: rgba(255, 167, 38, 0.1);
}

.restaurant-stats .facility-item.stats-item {
  cursor: default;
}

.restaurant-stats .facility-item.stats-item:hover {
  background: rgba(255, 167, 38, 0.08);
  border-color: rgba(255, 167, 38, 0.2);
  transform: none;
  box-shadow: none;
}

.restaurant-stats .facility-name {
  color: #ffcc80;
  font-weight: 500;
}

.restaurant-stats .facility-distance {
  color: #ff9800;
  font-size: 11px;
}

/* 配套设施图标样式 */
.facility-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.facility-icon svg {
  display: block;
}

.facility-name {
  color: #e3f2fd;
  font-size: 12px;
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.facility-distance {
  color: #4fc3f7;
  font-size: 11px;
  font-weight: bold;
  min-width: 50px;
  text-align: right;
  margin-left: auto;
  flex-shrink: 0;
}

/* Loading样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 20, 50, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 12px;
}

.facilities-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  flex-direction: column;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.spinner-ring {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(79, 195, 247, 0.2);
  border-top: 3px solid #4fc3f7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #4fc3f7;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无数据状态样式 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #666;
}

.no-data-icon {
  font-size: 32px;
  margin-bottom: 10px;
  opacity: 0.6;
}

.no-data-text {
  font-size: 14px;
  color: #888;
}



.building-placeholder {
  text-align: center;
  color: rgba(79, 195, 247, 0.8);
  background: rgba(0, 20, 50, 0.6);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.placeholder-text {
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

/* 底部数据区域 */
.bottom-data-section {
  display: flex;
  gap: 20px;
  height: auto;
}

/* GDP与税收整体容器 */
.gdp-tax-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  gap: 10px;
  flex: 0.5;
}

.gdp-tax-content {
  display: flex;
  flex-direction: row;
  gap: 15px;
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 8px;
}

.gdp-tax-item-compact {
  padding: 10px;
  text-align: center;
  flex: 1;
}

/* 核心数据整体容器 */
.core-data-container {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
}

/* 容器标题样式 */
.container-title {
  font-size: 14px;
  font-weight: bold;
  color: #4fc3f7;
  text-align: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  text-shadow: 0 0 8px rgba(79, 195, 247, 0.5);
}

/* 静态数据区域 */
.static-data-section {
  margin-bottom: 10px;
}

.static-data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: 1fr;
  gap: 8px;
}

/* 动态数据区域 */

.dynamic-data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.core-data-item-compact {
  padding: 9px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background: rgba(0, 30, 60, 0.8);
  border: 1px solid rgba(79, 195, 247, 0.2);
}

/* 动态数据项特殊样式 */

.data-icon-wrapper-compact {
  position: relative;
}

.data-icon-compact {
  width: 100px;
  height: 90px;
}

.data-content-compact {
  position: absolute;
  top: 15%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.data-value-compact {
  font-size: 14px;
  font-weight: bold;
  color: #4fc3f7;
}

.data-label-compact {
  font-size: 11px;
  color: #ffffff;
  margin-bottom: 2px;
  position: absolute;
  top: 36px;
}

.data-unit-compact {
  font-size: 11px;
  color: rgba(79, 195, 247, 0.7);
}

/* 空置率组件样式 */
.vacancy-rate-item {
  position: relative;
}

.vacancy-rate-container {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 核心数据指标区域 */
.core-data-section {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.core-data-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.core-data-item {
  text-align: center;
}

.data-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
}

.data-icon {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 0 10px rgba(79, 195, 247, 0.5));
  position: relative;
  top: 20px;
}

.data-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: row;
  align-items: center;
}

.data-value {
  color: #4fc3f7;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(79, 195, 247, 0.5);
  line-height: 1;
}

.data-unit {
  color: #81d4fa;
  font-size: 12px;
  margin-top: 2px;
}

.data-label {
  color: #e3f2fd;
  font-size: 15px;
  font-weight: 500;
}

/* GDP与税收总额区域 */
.gdp-tax-section {
  display: flex;
  gap: 20px;
}

.gdp-tax-item {
  flex: 1;
  background: rgba(79, 195, 247, 0.1);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.gdp-tax-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(79, 195, 247, 0.1),
    transparent
  );
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.gdp-tax-title {
  color: #81d4fa;
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: 500;
}

.gdp-tax-value {
  color: #4fc3f7;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(79, 195, 247, 0.5);
  margin-bottom: 5px;
}

.gdp-tax-value .unit {
  font-size: 14px;
  color: #81d4fa;
  margin-left: 5px;
}

.gdp-tax-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.trend-icon {
  color: #66bb6a;
  font-size: 16px;
  font-weight: bold;
}

.trend-text {
  color: #66bb6a;
  font-size: 12px;
}

/* 入驻企业总数 */
.enterprise-total {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.enterprise-icon {
  font-size: 48px;
  filter: drop-shadow(0 0 15px rgba(79, 195, 247, 0.5));
}

.enterprise-info {
  flex: 1;
}

.enterprise-title {
  color: #4fc3f7;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.enterprise-count {
  color: #4fc3f7;
  font-size: 32px;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(79, 195, 247, 0.5);
}

.unit {
  font-size: 18px;
  color: #81d4fa;
  margin-left: 5px;
}

/* 统计指标区域 */
.stats-section {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 5px 15px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(79, 195, 247, 0.2);
}

.stat-label {
  color: #81d4fa;
  font-size: 12px;
  margin-bottom: 8px;
}

.stat-value {
  color: #e3f2fd;
  font-size: 18px;
  font-weight: bold;
}

.stat-value.highlight {
  color: #ffa726;
}

/* 企业风险预警 */
.enterprise-risk-warning {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  // flex: 1;
  // max-height: 280px;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #4fc3f7;
  margin-bottom: 15px;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.panel-title-with-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.panel-title-with-filter .panel-title {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
  text-align: left;
}

.risk-filter {
  display: flex;
  align-items: center;
}

.filter-select {
  background: rgba(0, 30, 60, 0.8);
  border: 1px solid rgba(79, 195, 247, 0.4);
  border-radius: 6px;
  color: #4fc3f7;
  padding: 4px 8px;
  font-size: 12px;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:hover {
  border-color: rgba(79, 195, 247, 0.6);
  background: rgba(0, 30, 60, 0.9);
}

.filter-select:focus {
  border-color: #4fc3f7;
  box-shadow: 0 0 5px rgba(79, 195, 247, 0.3);
}

.filter-select option {
  background: rgba(0, 30, 60, 0.9);
  color: #4fc3f7;
  padding: 4px;
}

.warning-list {
  max-height: 270px;
  overflow-y: auto;
}

.warning-item {
  padding: 10px 12px;
  background: rgba(0, 30, 60, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(79, 195, 247, 0.2);
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.warning-item:hover {
  background: rgba(0, 30, 60, 0.8);
  transform: translateX(3px);
  border-color: rgba(79, 195, 247, 0.4);
}

.warning-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.warning-company {
  font-weight: bold;
  color: #4fc3f7;
  font-size: 13px;
  flex-shrink: 0;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.risk-items {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: flex-end;
  flex: 1;
}

.risk-item {
  display: flex;
  align-items: center;
  gap: 3px;
  background: rgba(0, 0, 0, 0.3);
  padding: 3px 6px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.risk-icon {
  font-size: 10px;
}

.risk-text {
  font-size: 10px;
  font-weight: bold;
  white-space: nowrap;
}

/* 中间区域 - 数据分析图表 */
.analysis-charts {
  display: flex;
  gap: 15px;
  height: auto;
}

.chart-item {
  flex: 1;
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 280px;
}

.chart-item.compact {
  height: 280px;
  padding: 12px;
}

.chart-title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
}

.chart-bg-icon {
  width: 120px;
  height: 40px;
  filter: drop-shadow(0 0 10px rgba(79, 195, 247, 0.3));
}

.chart-title {
  position: absolute;
  font-size: 16px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  text-align: center;
  z-index: 2;
}

.chart-container {
  flex: 1;
  min-height: 0;
  height: 220px;
}

/* 右侧面板 - TOP10企业列表 */
.top10-enterprise {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 右侧面板 - 行业TOP10 */
.industry-top10 {
  background: rgba(0, 20, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 12px;
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ranking-list {
  flex: 1;
  overflow-y: auto;
  max-height: 270px;
  padding-right: 5px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.ranking-item:hover {
  background: rgba(79, 195, 247, 0.2);
  transform: translateX(5px);
}

// .ranking-number {
//   padding:5px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   font-weight: bold;
//   font-size: 14px;
//   color: white;
//   text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
// }

// .ranking-number.rank-1 {
//   background: linear-gradient(135deg, #ffd700, #ffb300);
// }

// .ranking-number.rank-2 {
//   background: linear-gradient(135deg, #c0c0c0, #9e9e9e);
// }

// .ranking-number.rank-3 {
//   background: linear-gradient(135deg, #cd7f32, #8d5524);
// }

// .ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) {
//   background-image: url('@/assets/icon/other_bg.svg');
// }

.ranking-name {
  flex: 1;
  color: #e3f2fd;
  font-size: 13px;
  font-weight: 500;
}

.ranking-count {
  color: #ffa726;
  font-size: 14px;
  font-weight: bold;
}

/* 滚动条样式 */
.warning-list::-webkit-scrollbar,
.ranking-list::-webkit-scrollbar {
  width: 4px;
}

.warning-list::-webkit-scrollbar-track,
.ranking-list::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 2px;
}

.warning-list::-webkit-scrollbar-thumb,
.ranking-list::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.5);
  border-radius: 2px;
}

.warning-list::-webkit-scrollbar-thumb:hover,
.ranking-list::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.7);
}

/* 隐藏滚动条但保持滚动功能 */
.ranking-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(79, 195, 247, 0.5) rgba(79, 195, 247, 0.1);
}

/* 响应式设计 - 使用统一布局，移除main-content相关样式 */
@media (max-width: 1600px) {
  .stats-row {
    flex-direction: column;
    gap: 10px;
  }

  .analysis-charts {
    flex-direction: column;
    gap: 15px;
    max-width: none;
  }

  .chart-item.compact {
    min-height: 160px;
  }
}

@media (max-width: 1200px) {
  .unified-left-panel,
  .unified-right-panel {
    flex-direction: row;
    gap: 20px;
  }

  .analysis-charts {
    flex-direction: column;
    gap: 15px;
    max-width: none;
  }

  .chart-item.compact {
    min-height: 140px;
  }

  .building-title {
    font-size: 24px;
  }

  .enterprise-count {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .header-section {
    padding: 15px 20px;
  }

  .unified-left-panel,
  .unified-right-panel {
    flex-direction: column;
  }

  .analysis-charts {
    flex-direction: column;
    max-width: none;
  }

  .chart-item.compact {
    min-height: 120px;
  }

  .building-location {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .alert-section {
    margin: 0 20px 15px;
    padding: 12px 20px;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
